# Pixiv Tag Downloader 配置文件
# 请根据需要修改以下配置项

# 认证配置
auth:
  # Pixiv Cookie（必需）
  # 请从浏览器中获取有效的Pixiv登录Cookie并填入此处
  #
  # 获取方法：
  # 1. 在浏览器中登录 https://www.pixiv.net/
  # 2. 打开开发者工具（F12）
  # 3. 切换到 Network 标签
  # 4. 刷新页面或访问任意Pixiv页面
  # 5. 找到任意请求，查看 Request Headers 中的 Cookie 字段
  # 6. 复制完整的Cookie值（必须包含PHPSESSID等字段）
  #
  # 格式示例: "PHPSESSID=xxx; device_token=xxx; p_ab_id=xxx; ..."
  # 注意：当前Cookie可能已过期，请替换为有效的Pixiv登录Cookie
  cookie: "PHPSESSID=18104148_NOS6Zgs1Mh8DsX7YT4YlOSyEti79Mh7q;"

# 下载配置
download:
  # 下载方式: direct（直接下载）, aria2c（命令行）, aria2rpc（RPC）
  method: "direct"
  
  # 并发下载数（0表示使用CPU核心数）
  concurrency: 4
  
  # 随机延迟范围（秒）
  delay_range:
    min: 1
    max: 3
  
  # 重试次数
  retry_count: 3
  
  # 超时时间（秒）
  timeout_seconds: 30
  
  # 文件冲突处理策略: skip（跳过）, overwrite（覆盖）, rename（重命名）
  conflict_strategy: "skip"

# Aria2配置（仅在使用aria2rpc时需要）
aria2:
  # Aria2 RPC服务地址
  rpc_url: "ws://localhost:6800/jsonrpc"
  
  # Aria2 RPC密钥（可选）
  rpc_secret: ""
  
  # 跳过SSL验证（不推荐）
  skip_ssl_verify: false

# 存储配置
storage:
  # 输出根目录
  output_root_dir: "./downloads"
  
  # 单张图片命名模板
  # 可用变量: {uid}, {username}, {pid}, {title}, {type}, {upload_date}, {tags}, {r18}, {like_count}, {bookmark_count}, {ext}
  single_image_naming_template: "{upload_date}_{pid}_p0_{title}{ext}"
  
  # 多张图片子文件夹命名模板
  multi_image_subfolder_naming_template: "{upload_date}_{pid}_{title}"
  
  # 多张图片中单个文件命名模板
  # 额外变量: {page_index}, {page_count}
  multi_image_file_naming_template: "{title}_p{page_index}{ext}"
  
  # 小说文件命名模板
  novel_naming_template: "{upload_date}_{pid}_{title}.txt"
  
  # 默认目录结构模板
  default_path_template: "{uid}_{username}/{type}"

# HTTP配置
http:
  # User-Agent
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
  
  # Referer
  referer: "https://www.pixiv.net/"

# 日志配置
logging:
  # 日志级别: trace, debug, info, warn, error
  level: "info"
  
  # 日志文件保存路径
  file_path: "./logs"
  
  # 是否输出到控制台
  to_console: true
  
  # 是否输出到文件
  to_file: true

# 模板变量说明:
# {uid}           - Pixiv用户ID
# {username}      - Pixiv用户名（文件名安全处理）
# {pid}           - 作品ID
# {title}         - 作品标题（文件名安全处理）
# {type}          - 作品类型（Illust, Manga, Novel）
# {page_index}    - 页面索引（如 p0, p1, p01, p02等）
# {page_count}    - 作品总页数
# {series_title}  - 系列标题（如果存在，文件名安全处理）
# {series_id}     - 系列ID（如果存在）
# {upload_date}   - 上传日期（格式: YYYY-MM-DD）
# {tags}          - 作品标签（用下划线连接）
# {r18}           - R18标识（R18作品显示"R18"，否则为空）
# {like_count}    - 点赞数
# {bookmark_count}- 收藏数
# {ext}           - 文件扩展名（如 .jpg, .png）

# 使用示例:
# 1. 基本使用（交互模式）:
#    ./pixiv-downloader
#
# 2. 下载指定用户的所有作品:
#    ./pixiv-downloader -u 12345678 --all
#
# 3. 下载指定用户包含特定标签的作品:
#    ./pixiv-downloader -u 12345678 -t "原创,插画" -l or
#
# 4. 使用Aria2 RPC下载:
#    ./pixiv-downloader -u 12345678 --all --download-method aria2rpc
#
# 5. 自定义输出目录和并发数:
#    ./pixiv-downloader -u 12345678 --all --output-dir "/path/to/downloads" --threads 8

# 注意事项:
# 1. Cookie必须是有效的Pixiv登录Cookie
# 2. 如果要下载R18内容，确保Cookie对应的账号有相应权限
# 3. 建议设置合理的延迟和并发数，避免触发Pixiv的访问限制
# 4. 使用Aria2时，请确保Aria2服务正在运行
# 5. 文件名模板中的非法字符会被自动替换为下划线
