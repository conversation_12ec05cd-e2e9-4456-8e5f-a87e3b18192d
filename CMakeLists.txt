cmake_minimum_required(VERSION 3.15)

# 设置CMake策略
if(POLICY CMP0077)
    cmake_policy(SET CMP0077 NEW)
endif()

# 项目信息
project(PixivTagDownloader
    VERSION 1.0.0
    DESCRIPTION "高效可靠的Pixiv作品下载器"
    LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 编译选项
if(MSVC)
    add_compile_options(/W4 /utf-8)
    add_compile_definitions(_WIN32_WINNT=0x0601)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 包含目录
include_directories(${CMAKE_SOURCE_DIR}/include)

# 查找依赖包
find_package(Threads REQUIRED)
find_package(PkgConfig REQUIRED)

# 查找系统安装的包
find_package(nlohmann_json REQUIRED)
find_package(spdlog REQUIRED)
find_package(yaml-cpp REQUIRED)
find_package(CLI11 REQUIRED)

# 查找httplib - 使用CMake配置
find_package(httplib REQUIRED)
if(httplib_FOUND)
    message(STATUS "Found cpp-httplib package")
else()
    message(FATAL_ERROR "cpp-httplib not found")
endif()

# 源文件
set(SOURCES
    src/main.cpp
    src/common/types.cpp
    src/utils/string_utils.cpp
    src/utils/file_utils.cpp
    src/utils/time_utils.cpp
    src/utils/logger.cpp
    src/config/config_manager.cpp
    src/auth/auth_manager.cpp
    src/api/pixiv_api.cpp
    src/storage/storage_manager.cpp
    src/downloader/download_manager.cpp
    src/downloader/direct_downloader.cpp
    src/downloader/aria2_downloader.cpp
    src/cli/cli_manager.cpp
    src/core/core_logic.cpp
)



# 头文件
set(HEADERS
    include/utils/string_utils.h
    include/utils/file_utils.h
    include/utils/time_utils.h
    include/utils/logger.h
    include/config/config_manager.h
    include/auth/auth_manager.h
    include/api/pixiv_api.h
    include/storage/storage_manager.h
    include/downloader/download_manager.h
    include/downloader/direct_downloader.h
    include/downloader/aria2_downloader.h
    include/cli/cli_manager.h
    include/core/core_logic.h
    include/common/types.h
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# 链接库
target_link_libraries(${PROJECT_NAME}
    PRIVATE
    nlohmann_json::nlohmann_json
    spdlog::spdlog
    yaml-cpp
    CLI11::CLI11
    httplib::httplib
    Threads::Threads
)

# 检查是否需要额外的系统库
find_package(OpenSSL)
if(OpenSSL_FOUND)
    target_link_libraries(${PROJECT_NAME} PRIVATE OpenSSL::SSL OpenSSL::Crypto)
endif()

# 检查是否需要zlib
find_package(ZLIB)
if(ZLIB_FOUND)
    target_link_libraries(${PROJECT_NAME} PRIVATE ZLIB::ZLIB)
endif()

# 设置目标属性
set_target_properties(${PROJECT_NAME} PROPERTIES
    OUTPUT_NAME "pixiv-downloader"
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)

# 安装规则
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

# 复制配置文件示例
install(FILES config/config.yaml.example
    DESTINATION .
    RENAME config.yaml.example
)

# CPack配置
include(CPack)
set(CPACK_PACKAGE_NAME "PixivTagDownloader")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY ${PROJECT_DESCRIPTION})
set(CPACK_PACKAGE_VENDOR "PixivTagDownloader Team")
