#include <iostream>
#include <httplib.h>
#include <string>

int main() {
    std::string cookie = "PHPSESSID=18104148_NOS6Zgs1Mh8DsX7YT4YlOSyEti79Mh7q;";
    
    std::cout << "测试Cookie: " << cookie << std::endl;
    
    try {
        httplib::Client client("https://www.pixiv.net");
        client.set_follow_location(false);
        client.set_connection_timeout(10, 0);
        client.set_read_timeout(10, 0);
        
        httplib::Headers headers;
        headers.emplace("Cookie", cookie);
        headers.emplace("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        headers.emplace("Referer", "https://www.pixiv.net/");
        headers.emplace("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
        headers.emplace("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        
        std::vector<std::string> test_urls = {
            "/",
            "/bookmark.php",
            "/setting_user.php"
        };
        
        for (const auto& url : test_urls) {
            std::cout << "\n测试访问: " << url << std::endl;
            auto result = client.Get(url.c_str(), headers);
            
            if (!result) {
                std::cout << "请求失败" << std::endl;
                continue;
            }
            
            std::cout << "状态码: " << result->status << std::endl;
            
            if (result->status == 302 || result->status == 301) {
                auto location = result->get_header_value("Location");
                std::cout << "重定向到: " << location << std::endl;
            }
            
            if (result->status == 200) {
                std::string body = result->body;
                std::cout << "响应体长度: " << body.length() << std::endl;
                
                // 检查关键字
                bool has_login = body.find("login") != std::string::npos;
                bool has_signin = body.find("signin") != std::string::npos;
                bool has_logout = body.find("logout") != std::string::npos;
                bool has_user = body.find("user") != std::string::npos;
                
                std::cout << "包含 'login': " << (has_login ? "是" : "否") << std::endl;
                std::cout << "包含 'signin': " << (has_signin ? "是" : "否") << std::endl;
                std::cout << "包含 'logout': " << (has_logout ? "是" : "否") << std::endl;
                std::cout << "包含 'user': " << (has_user ? "是" : "否") << std::endl;
                
                // 显示前500个字符
                std::cout << "响应体前500字符:" << std::endl;
                std::cout << body.substr(0, 500) << std::endl;
            }
        }
        
    } catch (const std::exception& e) {
        std::cout << "异常: " << e.what() << std::endl;
    }
    
    return 0;
}
