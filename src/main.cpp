/**
 * @file main.cpp
 * @brief Pixiv Tag Downloader 主程序入口
 * <AUTHOR> Team
 * @date 2024
 */

#include "core/core_logic.h"
#include "utils/logger.h"
#include <iostream>
#include <exception>
#include <iomanip>
#include <csignal>

#ifdef _WIN32
#include <windows.h>
#endif

/**
 * @brief 程序版本信息
 */
const char* VERSION = "1.0.0";
const char* BUILD_DATE = __DATE__;
const char* BUILD_TIME = __TIME__;

/**
 * @brief 显示程序信息
 */
void ShowProgramInfo() {
    std::cout << "==================================================" << std::endl;
    std::cout << "    Pixiv Tag Downloader v" << VERSION << std::endl;
    std::cout << "    高效可靠的Pixiv作品下载器" << std::endl;
    std::cout << "    构建时间: " << BUILD_DATE << " " << BUILD_TIME << std::endl;
    std::cout << "==================================================" << std::endl;
    std::cout << std::endl;
}

/**
 * @brief 显示使用说明
 */
void ShowUsage() {
    std::cout << "使用方法:" << std::endl;
    std::cout << "  交互模式: pixiv-downloader" << std::endl;
    std::cout << "  命令行模式: pixiv-downloader [选项]" << std::endl;
    std::cout << std::endl;
    std::cout << "主要选项:" << std::endl;
    std::cout << "  -u, --uid <UID>              目标Pixiv用户ID（必需）" << std::endl;
    std::cout << "  -t, --tags <TAG1,TAG2,...>   要下载的Tag列表，多个Tag用逗号分隔" << std::endl;
    std::cout << "  -l, --logic <and|or|not>     Tag过滤逻辑，默认 or" << std::endl;
    std::cout << "  --all                        下载目标用户的所有作品，不做Tag筛选" << std::endl;
    std::cout << "  -T, --type <类型>            下载作品类型 (Illust,Manga,Novel,all)" << std::endl;
    std::cout << "  -c, --config <PATH>          配置文件路径，默认 config.yaml" << std::endl;
    std::cout << "  --output-dir <PATH>          输出根目录" << std::endl;
    std::cout << "  --download-method <方式>     下载方式 (direct|aria2c|aria2rpc)" << std::endl;
    std::cout << "  --threads <NUM>              并发下载任务数" << std::endl;
    std::cout << "  --delay <MIN-MAX>            随机延迟范围（秒），如 1-3" << std::endl;
    std::cout << "  --log-level <级别>           日志级别 (trace|debug|info|warn|error)" << std::endl;
    std::cout << "  -h, --help                   显示此帮助信息" << std::endl;
    std::cout << "  -v, --version                显示版本信息" << std::endl;
    std::cout << std::endl;
    std::cout << "示例:" << std::endl;
    std::cout << "  # 交互模式" << std::endl;
    std::cout << "  pixiv-downloader" << std::endl;
    std::cout << std::endl;
    std::cout << "  # 下载指定用户的所有作品" << std::endl;
    std::cout << "  pixiv-downloader -u 12345678 --all" << std::endl;
    std::cout << std::endl;
    std::cout << "  # 下载指定用户包含特定标签的作品" << std::endl;
    std::cout << "  pixiv-downloader -u 12345678 -t \"原创,插画\" -l or" << std::endl;
    std::cout << std::endl;
    std::cout << "  # 使用Aria2 RPC下载" << std::endl;
    std::cout << "  pixiv-downloader -u 12345678 --all --download-method aria2rpc" << std::endl;
    std::cout << std::endl;
}

/**
 * @brief 显示版本信息
 */
void ShowVersion() {
    std::cout << "Pixiv Tag Downloader v" << VERSION << std::endl;
    std::cout << "构建时间: " << BUILD_DATE << " " << BUILD_TIME << std::endl;
    std::cout << "Copyright (c) 2024 PixivTagDownloader Team" << std::endl;
}

/**
 * @brief 程序主入口
 * @param argc 命令行参数数量
 * @param argv 命令行参数数组
 * @return 程序退出码
 */
int main(int argc, char* argv[]) {
    try {
        // 显示程序信息
        ShowProgramInfo();
        
        // 检查是否请求帮助或版本信息
        for (int i = 1; i < argc; ++i) {
            std::string arg = argv[i];
            if (arg == "-h" || arg == "--help") {
                ShowUsage();
                return 0;
            } else if (arg == "-v" || arg == "--version") {
                ShowVersion();
                return 0;
            }
        }
        
        // 创建核心逻辑实例
        pixiv::core::CoreLogic core_logic;
        
        // 设置状态回调
        core_logic.SetStatusCallback([](const std::string& message) {
            std::cout << "[状态] " << message << std::endl;
        });
        
        // 设置进度回调
        core_logic.SetProgressCallback([](const pixiv::DownloadProgress& progress) {
            if (progress.total_files > 0) {
                double percent = progress.GetOverallProgress();
                std::cout << "\r[进度] " << progress.completed_files << "/" << progress.total_files 
                         << " (" << std::fixed << std::setprecision(1) << percent << "%) "
                         << "当前: " << progress.current_file << std::flush;
            }
        });
        
        // 运行应用程序
        auto result = core_logic.Run(argc, argv);
        
        // 根据结果返回相应的退出码
        int exit_code = 0;
        switch (result) {
            case pixiv::core::AppResult::SUCCESS:
                std::cout << std::endl << "下载完成！" << std::endl;
                exit_code = 0;
                break;

            case pixiv::core::AppResult::CONFIG_ERROR:
                std::cerr << "配置错误，请检查配置文件" << std::endl;
                exit_code = 1;
                break;

            case pixiv::core::AppResult::AUTH_ERROR:
                std::cerr << "认证失败，请检查Cookie是否有效" << std::endl;
                exit_code = 2;
                break;

            case pixiv::core::AppResult::NETWORK_ERROR:
                std::cerr << "网络错误，请检查网络连接" << std::endl;
                exit_code = 3;
                break;

            case pixiv::core::AppResult::USER_NOT_FOUND:
                std::cerr << "用户不存在，请检查UID是否正确" << std::endl;
                exit_code = 4;
                break;

            case pixiv::core::AppResult::NO_ARTWORKS:
                std::cout << "没有找到符合条件的作品" << std::endl;
                exit_code = 0;
                break;

            case pixiv::core::AppResult::DOWNLOAD_ERROR:
                std::cerr << "下载过程中发生错误" << std::endl;
                exit_code = 5;
                break;

            case pixiv::core::AppResult::STORAGE_ERROR:
                std::cerr << "存储错误，请检查磁盘空间和权限" << std::endl;
                exit_code = 6;
                break;

            case pixiv::core::AppResult::USER_CANCELLED:
                std::cout << "用户取消操作" << std::endl;
                exit_code = 0;
                break;

            default:
                std::cerr << "未知错误" << std::endl;
                exit_code = 99;
                break;
        }

        // 在程序正常退出前关闭日志系统
        try {
            pixiv::utils::Logger::Shutdown();
        } catch (...) {
            // 忽略日志关闭时的异常
        }

        return exit_code;
        
    } catch (const std::exception& e) {
        std::cerr << "程序运行时发生异常: " << e.what() << std::endl;
        return 98;
    } catch (...) {
        std::cerr << "程序运行时发生未知异常" << std::endl;
        return 97;
    }
}

/**
 * @brief 程序异常终止处理函数
 */
void terminate_handler() {
    std::cerr << "程序异常终止" << std::endl;
    
    // 尝试关闭日志系统
    try {
        pixiv::utils::Logger::Shutdown();
    } catch (...) {
        // 忽略日志关闭时的异常
    }
    
    std::abort();
}

/**
 * @brief 程序初始化
 */
class ProgramInitializer {
public:
    ProgramInitializer() {
        // 设置异常终止处理函数
        std::set_terminate(terminate_handler);
        
        // 设置控制台编码（Windows）
#ifdef _WIN32
        SetConsoleOutputCP(CP_UTF8);
        SetConsoleCP(CP_UTF8);
#endif
    }
    
    ~ProgramInitializer() {
        // 程序退出时清理
        // 注意：不在这里关闭日志系统，避免与其他地方重复关闭
    }
};

// 全局初始化器
static ProgramInitializer g_initializer;
