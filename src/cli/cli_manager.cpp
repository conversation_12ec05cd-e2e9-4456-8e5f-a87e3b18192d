/**
 * @file cli_manager.cpp
 * @brief CLI管理器实现
 * <AUTHOR> Team
 * @date 2024
 */

#include "cli/cli_manager.h"
#include "utils/logger.h"
#include "utils/string_utils.h"
#include <CLI/CLI.hpp>
#include <iostream>
#include <iomanip>

namespace pixiv {
namespace cli {

CliManager::CliManager() : color_output_enabled_(true), interactive_mode_(false) {
    InitializeCli();
}

CliManager::~CliManager() = default;

CommandLineArgs CliManager::ParseCommandLine(int argc, char* argv[]) {
    CommandLineArgs args;
    
    CLI::App app{"Pixiv Tag Downloader - 高效可靠的Pixiv作品下载器"};
    
    // 基本选项
    app.add_option("-u,--uid", args.uid, "目标Pixiv用户ID（必需）");
    app.add_option("-t,--tags", args.tags, "要下载的Tag列表，多个Tag用逗号分隔");
    
    std::string logic_str = "or";
    app.add_option("-l,--logic", logic_str, "Tag过滤逻辑 (and|or|not)")
        ->check(CLI::IsMember({"and", "or", "not"}));
    
    app.add_flag("--all", args.download_all, "下载目标用户的所有作品，不做Tag筛选");
    
    std::vector<std::string> type_strs;
    app.add_option("-T,--type", type_strs, "下载作品类型 (Illust,Manga,Novel,all)");
    
    app.add_option("-c,--config", args.config_path, "配置文件路径")
        ->default_val("config.yaml");
    
    app.add_option("--output-dir", args.output_dir, "输出根目录");
    
    std::string method_str = "direct";
    app.add_option("--download-method", method_str, "下载方式 (direct|aria2c|aria2rpc)")
        ->check(CLI::IsMember({"direct", "aria2c", "aria2rpc"}));
    
    app.add_option("--aria2rpc-url", args.aria2_rpc_url, "Aria2 RPC服务地址");
    app.add_option("--aria2rpc-secret", args.aria2_rpc_secret, "Aria2 RPC授权密钥");
    
    app.add_option("--threads,--concurrency", args.concurrency, "并发下载任务数");
    
    std::string delay_str;
    app.add_option("--delay", delay_str, "随机延迟范围（秒），如 1-3");
    
    std::string conflict_str = "skip";
    app.add_option("--conflict", conflict_str, "文件冲突处理策略 (skip|overwrite|rename)")
        ->check(CLI::IsMember({"skip", "overwrite", "rename"}));
    
    app.add_option("--log-level", args.log_level, "日志级别 (trace|debug|info|warn|error)")
        ->check(CLI::IsMember({"trace", "debug", "info", "warn", "error"}));

    // 注意：-h,--help 是CLI11的内置选项，不需要手动添加
    app.add_flag("-v,--version", args.version, "显示版本信息");
    app.add_flag("-i,--interactive", args.interactive, "启动交互模式");
    
    try {
        app.parse(argc, argv);

        // 转换字符串参数
        args.tag_logic = StringToTagFilterLogic(logic_str);
        args.download_method = StringToDownloadMethod(method_str);

        // 解析作品类型
        if (!type_strs.empty()) {
            for (const auto& type_str : type_strs) {
                if (utils::StringUtils::ToLower(type_str) == "all") {
                    args.types = {ArtworkType::ILLUST, ArtworkType::MANGA, ArtworkType::NOVEL};
                    break;
                } else {
                    args.types.push_back(StringToArtworkType(type_str));
                }
            }
        } else {
            args.types = {ArtworkType::ILLUST, ArtworkType::MANGA, ArtworkType::NOVEL};
        }

        // 解析延迟范围
        if (!delay_str.empty()) {
            auto parts = utils::StringUtils::Split(delay_str, "-");
            if (parts.size() == 2) {
                args.delay_range.first = std::stoi(parts[0]);
                args.delay_range.second = std::stoi(parts[1]);
            }
        }

        // 解析冲突策略
        if (conflict_str == "skip") {
            args.conflict_strategy = ConflictStrategy::SKIP;
        } else if (conflict_str == "overwrite") {
            args.conflict_strategy = ConflictStrategy::OVERWRITE;
        } else if (conflict_str == "rename") {
            args.conflict_strategy = ConflictStrategy::RENAME;
        }

        // 解析标签字符串
        if (!args.tags.empty()) {
            std::vector<std::string> all_tags;
            for (const auto& tag_str : args.tags) {
                auto tags = utils::StringUtils::Split(tag_str, ",");
                all_tags.insert(all_tags.end(), tags.begin(), tags.end());
            }
            args.tags = all_tags;
        }

    } catch (const CLI::CallForHelp& e) {
        // CLI11自动处理help，这里不需要设置help标志
        throw; // 重新抛出，让调用者处理
    } catch (const CLI::ParseError& e) {
        ShowError("命令行参数解析错误: " + std::string(e.what()));
        args.help = true;
    }
    
    return args;
}

void CliManager::ShowHelp() {
    std::cout << "Pixiv Tag Downloader v1.0.0" << std::endl;
    std::cout << "高效可靠的Pixiv作品下载器" << std::endl;
    std::cout << std::endl;
    
    std::cout << "使用方法:" << std::endl;
    std::cout << "  交互模式: pixiv-downloader" << std::endl;
    std::cout << "  命令行模式: pixiv-downloader [选项]" << std::endl;
    std::cout << std::endl;
    
    std::cout << "主要选项:" << std::endl;
    std::cout << "  -u, --uid <UID>              目标Pixiv用户ID（必需）" << std::endl;
    std::cout << "  -t, --tags <TAG1,TAG2,...>   要下载的Tag列表，多个Tag用逗号分隔" << std::endl;
    std::cout << "  -l, --logic <and|or|not>     Tag过滤逻辑，默认 or" << std::endl;
    std::cout << "  --all                        下载目标用户的所有作品，不做Tag筛选" << std::endl;
    std::cout << "  -T, --type <类型>            下载作品类型 (Illust,Manga,Novel,all)" << std::endl;
    std::cout << "  -c, --config <PATH>          配置文件路径，默认 config.yaml" << std::endl;
    std::cout << "  --output-dir <PATH>          输出根目录" << std::endl;
    std::cout << "  --download-method <方式>     下载方式 (direct|aria2c|aria2rpc)" << std::endl;
    std::cout << "  --threads <NUM>              并发下载任务数" << std::endl;
    std::cout << "  --delay <MIN-MAX>            随机延迟范围（秒），如 1-3" << std::endl;
    std::cout << "  --log-level <级别>           日志级别 (trace|debug|info|warn|error)" << std::endl;
    std::cout << "  -h, --help                   显示此帮助信息" << std::endl;
    std::cout << "  -v, --version                显示版本信息" << std::endl;
    std::cout << std::endl;
    
    std::cout << "示例:" << std::endl;
    std::cout << "  # 交互模式" << std::endl;
    std::cout << "  pixiv-downloader" << std::endl;
    std::cout << std::endl;
    std::cout << "  # 下载指定用户的所有作品" << std::endl;
    std::cout << "  pixiv-downloader -u 12345678 --all" << std::endl;
    std::cout << std::endl;
    std::cout << "  # 下载指定用户包含特定标签的作品" << std::endl;
    std::cout << "  pixiv-downloader -u 12345678 -t \"原创,插画\" -l or" << std::endl;
    std::cout << std::endl;
}

void CliManager::ShowVersion() {
    std::cout << "Pixiv Tag Downloader v1.0.0" << std::endl;
    std::cout << "构建时间: " << __DATE__ << " " << __TIME__ << std::endl;
    std::cout << "Copyright (c) 2024 PixivTagDownloader Team" << std::endl;
}

InteractiveResult CliManager::StartInteractiveMode(const std::vector<std::string>& available_tags) {
    interactive_mode_ = true;
    InteractiveResult result;
    
    ShowInfo("欢迎使用 Pixiv Tag Downloader 交互模式");
    std::cout << std::endl;
    
    // 获取用户UID
    while (result.uid.empty()) {
        result.uid = GetUserInput("请输入目标Pixiv用户ID: ");
        if (!utils::StringUtils::IsNumeric(result.uid)) {
            ShowError("用户ID必须是数字");
            result.uid.clear();
        }
    }
    
    // 选择作品类型
    ShowInfo("选择要下载的作品类型:");
    result.types = ShowArtworkTypeSelectionMenu();
    
    // 选择下载方式
    ShowInfo("选择下载方式:");
    std::cout << "1. 下载所有作品" << std::endl;
    std::cout << "2. 手动输入标签" << std::endl;
    std::cout << "3. 从作品标签列表中选择" << std::endl;
    
    std::string choice = GetUserInput("请选择 (1-3): ", "1");
    
    if (choice == "1") {
        result.download_all = true;
    } else if (choice == "2") {
        std::string tags_input = GetUserInput("请输入标签（用逗号分隔）: ");
        result.selected_tags = utils::StringUtils::Split(tags_input, ",");
    } else if (choice == "3") {
        if (!available_tags.empty()) {
            auto indices = ShowTagSelectionMenu(available_tags, true);
            for (int idx : indices) {
                if (idx >= 0 && idx < available_tags.size()) {
                    result.selected_tags.push_back(available_tags[idx]);
                }
            }
        } else {
            ShowWarning("没有可用的标签列表，将下载所有作品");
            result.download_all = true;
        }
    }
    
    // 选择标签过滤逻辑
    if (!result.selected_tags.empty()) {
        result.tag_logic = ShowTagFilterLogicMenu();
    }
    
    // 确认下载
    ShowInfo("下载设置:");
    std::cout << "  用户ID: " << result.uid << std::endl;
    std::cout << "  作品类型: ";
    for (size_t i = 0; i < result.types.size(); ++i) {
        if (i > 0) std::cout << ", ";
        std::cout << ArtworkTypeToString(result.types[i]);
    }
    std::cout << std::endl;
    
    if (result.download_all) {
        std::cout << "  下载范围: 所有作品" << std::endl;
    } else {
        std::cout << "  标签: ";
        for (size_t i = 0; i < result.selected_tags.size(); ++i) {
            if (i > 0) std::cout << ", ";
            std::cout << result.selected_tags[i];
        }
        std::cout << std::endl;
        std::cout << "  过滤逻辑: " << TagFilterLogicToString(result.tag_logic) << std::endl;
    }
    
    result.continue_download = AskConfirmation("确认开始下载？", true);
    
    interactive_mode_ = false;
    return result;
}

void CliManager::DisplayProgress(const DownloadProgress& progress) {
    if (progress_callback_) {
        progress_callback_(progress);
    } else {
        // 默认进度显示
        if (progress.total_files > 0) {
            double percent = progress.GetOverallProgress();
            std::cout << "\r[进度] " << progress.completed_files << "/" << progress.total_files 
                     << " (" << std::fixed << std::setprecision(1) << percent << "%) "
                     << "当前: " << progress.current_file << std::flush;
        }
    }
}

void CliManager::SetProgressDisplayCallback(ProgressDisplayCallback callback) {
    progress_callback_ = callback;
}

void CliManager::ShowError(const std::string& message) {
    std::cout << FormatColorText("[错误] " + message, "31", true) << std::endl;
}

void CliManager::ShowWarning(const std::string& message) {
    std::cout << FormatColorText("[警告] " + message, "33", true) << std::endl;
}

void CliManager::ShowInfo(const std::string& message) {
    std::cout << FormatColorText("[信息] " + message, "34", false) << std::endl;
}

void CliManager::ShowSuccess(const std::string& message) {
    std::cout << FormatColorText("[成功] " + message, "32", true) << std::endl;
}

bool CliManager::AskConfirmation(const std::string& message, bool default_yes) {
    std::string prompt = message + (default_yes ? " [Y/n]: " : " [y/N]: ");
    std::string input = GetUserInput(prompt);
    
    if (input.empty()) {
        return default_yes;
    }
    
    std::string lower_input = utils::StringUtils::ToLower(input);
    return lower_input == "y" || lower_input == "yes" || lower_input == "是";
}

std::string CliManager::GetUserInput(const std::string& prompt, const std::string& default_value) {
    std::cout << prompt;
    std::string input;
    std::getline(std::cin, input);
    
    if (input.empty() && !default_value.empty()) {
        return default_value;
    }
    
    return utils::StringUtils::Trim(input);
}

std::string CliManager::GetPasswordInput(const std::string& prompt) {
    std::cout << prompt;
    return GetHiddenInput();
}

std::vector<int> CliManager::ShowTagSelectionMenu(const std::vector<std::string>& tags, bool allow_multiple) {
    // 简化实现
    std::vector<int> selected;
    
    std::cout << "可用标签:" << std::endl;
    for (size_t i = 0; i < tags.size() && i < 20; ++i) {
        std::cout << "  " << (i + 1) << ". " << tags[i] << std::endl;
    }
    
    std::string input = GetUserInput("请选择标签编号（用逗号分隔）: ");
    auto indices_str = utils::StringUtils::Split(input, ",");
    
    for (const auto& idx_str : indices_str) {
        try {
            int idx = std::stoi(utils::StringUtils::Trim(idx_str)) - 1;
            if (idx >= 0 && idx < tags.size()) {
                selected.push_back(idx);
            }
        } catch (...) {
            // 忽略无效输入
        }
    }
    
    return selected;
}

std::vector<ArtworkType> CliManager::ShowArtworkTypeSelectionMenu() {
    std::cout << "1. 插画 (Illust)" << std::endl;
    std::cout << "2. 漫画 (Manga)" << std::endl;
    std::cout << "3. 小说 (Novel)" << std::endl;
    std::cout << "4. 全部" << std::endl;
    
    std::string input = GetUserInput("请选择类型编号（用逗号分隔）: ", "4");
    auto choices = utils::StringUtils::Split(input, ",");
    
    std::vector<ArtworkType> types;
    
    for (const auto& choice : choices) {
        std::string trimmed = utils::StringUtils::Trim(choice);
        if (trimmed == "1") {
            types.push_back(ArtworkType::ILLUST);
        } else if (trimmed == "2") {
            types.push_back(ArtworkType::MANGA);
        } else if (trimmed == "3") {
            types.push_back(ArtworkType::NOVEL);
        } else if (trimmed == "4") {
            return {ArtworkType::ILLUST, ArtworkType::MANGA, ArtworkType::NOVEL};
        }
    }
    
    if (types.empty()) {
        return {ArtworkType::ILLUST, ArtworkType::MANGA, ArtworkType::NOVEL};
    }
    
    return types;
}

TagFilterLogic CliManager::ShowTagFilterLogicMenu() {
    std::cout << "标签过滤逻辑:" << std::endl;
    std::cout << "1. OR - 包含任意一个标签" << std::endl;
    std::cout << "2. AND - 包含所有标签" << std::endl;
    std::cout << "3. NOT - 排除包含标签的作品" << std::endl;
    
    std::string choice = GetUserInput("请选择 (1-3): ", "1");
    
    if (choice == "2") {
        return TagFilterLogic::AND;
    } else if (choice == "3") {
        return TagFilterLogic::NOT;
    } else {
        return TagFilterLogic::OR;
    }
}

void CliManager::ClearScreen() {
#ifdef _WIN32
    system("cls");
#else
    system("clear");
#endif
}

void CliManager::PauseForUser(const std::string& message) {
    std::cout << message;
    std::cin.get();
}

void CliManager::SetConsoleTitle(const std::string& title) {
#ifdef _WIN32
    SetConsoleTitleA(title.c_str());
#else
    std::cout << "\033]0;" << title << "\007";
#endif
}

bool CliManager::SupportsColor() const {
    return color_output_enabled_;
}

void CliManager::SetColorOutput(bool enable) {
    color_output_enabled_ = enable;
}

void CliManager::InitializeCli() {
    // 初始化CLI设置
    SetupSignalHandlers();
}

void CliManager::SetupSignalHandlers() {
    // 设置信号处理器
}

int CliManager::GetConsoleWidth() const {
    return 80; // 默认宽度
}

std::string CliManager::FormatColorText(const std::string& text, 
                                       const std::string& color, 
                                       bool bold) const {
    if (!color_output_enabled_) {
        return text;
    }
    
    std::string format = "\033[" + color + "m";
    if (bold) {
        format = "\033[1;" + color + "m";
    }
    
    return format + text + "\033[0m";
}

std::string CliManager::CreateProgressBar(double progress, int width) const {
    int filled = static_cast<int>(progress * width / 100.0);
    std::string bar = "[";
    
    for (int i = 0; i < width; ++i) {
        if (i < filled) {
            bar += "=";
        } else if (i == filled) {
            bar += ">";
        } else {
            bar += " ";
        }
    }
    
    bar += "]";
    return bar;
}

std::string CliManager::FormatFileSize(size_t bytes) const {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    int unit = 0;
    double size = static_cast<double>(bytes);
    
    while (size >= 1024.0 && unit < 4) {
        size /= 1024.0;
        unit++;
    }
    
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2) << size << " " << units[unit];
    return oss.str();
}

std::string CliManager::FormatDuration(double seconds) const {
    int hours = static_cast<int>(seconds / 3600);
    int minutes = static_cast<int>((seconds - hours * 3600) / 60);
    int secs = static_cast<int>(seconds) % 60;
    
    std::ostringstream oss;
    if (hours > 0) {
        oss << hours << "h " << minutes << "m " << secs << "s";
    } else if (minutes > 0) {
        oss << minutes << "m " << secs << "s";
    } else {
        oss << secs << "s";
    }
    
    return oss.str();
}

std::vector<int> CliManager::ShowPaginatedMenu(const std::vector<std::string>& items,
                                              const std::string& title,
                                              bool allow_multiple,
                                              int items_per_page) {
    // 简化实现
    return ShowTagSelectionMenu(items, allow_multiple);
}

std::vector<int> CliManager::ShowSearchFilterMenu(const std::vector<std::string>& items,
                                                  const std::string& title) {
    // 简化实现
    return {};
}

bool CliManager::ValidateInput(const std::string& input, const std::string& type) const {
    // 简化实现
    return !input.empty();
}

void CliManager::HandleInterruptSignal(int signal) {
    // 处理中断信号
}

std::string CliManager::GetHiddenInput() const {
    // 简化实现，实际项目中需要隐藏输入
    std::string input;
    std::getline(std::cin, input);
    return input;
}

} // namespace cli
} // namespace pixiv
