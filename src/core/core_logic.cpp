/**
 * @file core_logic.cpp
 * @brief 核心业务逻辑实现
 * <AUTHOR> Team
 * @date 2024
 */

#include "core/core_logic.h"
#include "utils/logger.h"
#include <iostream>
#include <csignal>
#include <CLI/CLI.hpp>

namespace pixiv {
namespace core {

// 静态成员初始化
CoreLogic* CoreLogic::instance_ = nullptr;

CoreLogic::CoreLogic() : cancelled_(false) {
    instance_ = this;
    SetupSignalHandlers();
}

CoreLogic::~CoreLogic() {
    Cleanup();
    instance_ = nullptr;
}

AppResult CoreLogic::Run(int argc, char* argv[]) {
    try {
        // 初始化应用程序
        if (!Initialize()) {
            return AppResult::CONFIG_ERROR;
        }
        
        // 创建CLI管理器并解析命令行参数
        cli_manager_ = std::make_unique<cli::CliManager>();

        cli::CommandLineArgs args;
        try {
            args = cli_manager_->ParseCommandLine(argc, argv);
        } catch (const CLI::CallForHelp& e) {
            // CLI11自动显示帮助信息并退出
            return AppResult::SUCCESS;
        }

        // 检查是否显示版本信息
        if (args.version) {
            cli_manager_->ShowVersion();
            return AppResult::SUCCESS;
        }
        
        // 根据参数决定运行模式
        if (args.interactive || args.uid.empty()) {
            return RunInteractiveMode();
        } else {
            return RunCommandLineMode(args);
        }
        
    } catch (const std::exception& e) {
        return HandleError("运行时异常: " + std::string(e.what()), AppResult::UNKNOWN_ERROR);
    }
}

void CoreLogic::SetStatusCallback(StatusCallback callback) {
    status_callback_ = callback;
}

void CoreLogic::SetProgressCallback(ProgressCallback callback) {
    progress_callback_ = callback;
}

const DownloadStats& CoreLogic::GetDownloadStats() const {
    return download_stats_;
}

void CoreLogic::Cancel() {
    cancelled_ = true;
    UpdateStatus("用户取消操作");
    
    if (download_manager_) {
        download_manager_->StopDownload();
    }
}

bool CoreLogic::IsCancelled() const {
    return cancelled_;
}

bool CoreLogic::Initialize(const std::string& config_path) {
    try {
        // 创建配置管理器
        config_manager_ = std::make_unique<config::ConfigManager>();
        
        // 加载配置文件
        if (!config_manager_->LoadConfig(config_path)) {
            LOG_ERROR("配置文件加载失败");
            return false;
        }
        
        const auto& config = config_manager_->GetConfig();
        
        // 初始化日志系统
        if (!utils::Logger::Initialize(config.log_level, 
                                      config.log_to_console,
                                      config.log_to_file,
                                      config.log_file_path)) {
            std::cerr << "日志系统初始化失败" << std::endl;
            return false;
        }
        
        LOG_INFO("Pixiv Tag Downloader 启动");
        LOG_INFO("配置文件: {}", config_path);
        
        // 验证配置
        if (!ValidateConfiguration()) {
            return false;
        }
        
        // 创建其他管理器
        auth_manager_ = std::make_unique<auth::AuthManager>();
        pixiv_api_ = std::make_unique<api::PixivApi>();

        // 设置认证信息
        if (!auth_manager_->SetCookie(config.cookie)) {
            LOG_ERROR("Cookie设置失败");
            return false;
        }

        // 配置PixivAPI
        pixiv_api_->SetCookie(config.cookie);
        pixiv_api_->SetUserAgent(config.user_agent);
        pixiv_api_->SetReferer(config.referer);

        // 验证认证
        if (!ValidateAuthentication()) {
            return false;
        }

        // 获取并显示当前用户信息
        auto current_user = auth_manager_->GetUserInfo();
        if (current_user) {
            LOG_INFO("当前用户: {} (UID: {})", current_user->username, current_user->uid);
        } else {
            LOG_INFO("当前用户: 未获取到用户信息");
        }

        LOG_INFO("应用程序初始化完成");
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("初始化时发生异常: {}", e.what());
        return false;
    }
}

AppResult CoreLogic::RunCommandLineMode(const cli::CommandLineArgs& args) {
    LOG_INFO("运行命令行模式");
    
    // 应用命令行参数覆盖配置
    config_manager_->ApplyCommandLineOverrides(
        args.uid, args.tags, args.tag_logic, args.types,
        args.download_all, args.output_dir, args.download_method,
        args.aria2_rpc_url, args.aria2_rpc_secret, args.concurrency,
        args.delay_range, args.conflict_strategy, args.log_level
    );
    
    // 执行下载任务
    return ExecuteDownloadTask(args.uid, args.tags, args.tag_logic, 
                              args.types, args.download_all);
}

AppResult CoreLogic::RunInteractiveMode() {
    LOG_INFO("运行交互式模式");
    
    if (!cli_manager_) {
        cli_manager_ = std::make_unique<cli::CliManager>();
    }
    
    // 启动交互式界面
    auto result = cli_manager_->StartInteractiveMode();
    
    if (!result.continue_download) {
        return AppResult::USER_CANCELLED;
    }
    
    // 执行下载任务
    return ExecuteDownloadTask(result.uid, result.selected_tags, 
                              result.tag_logic, result.types, 
                              result.download_all);
}

AppResult CoreLogic::ExecuteDownloadTask(const std::string& uid,
                                        const std::vector<std::string>& tags,
                                        TagFilterLogic tag_logic,
                                        const std::vector<ArtworkType>& types,
                                        bool download_all) {
    if (IsCancelled()) {
        return AppResult::USER_CANCELLED;
    }
    
    UpdateStatus("开始执行下载任务");
    LOG_INFO("目标用户UID: {}", uid);
    
    // 这里是简化实现，实际项目中需要完整实现所有功能
    UpdateStatus("获取用户信息...");
    auto user_info = GetUserInfo(uid);
    if (!user_info) {
        return HandleError("无法获取用户信息", AppResult::USER_NOT_FOUND);
    }
    
    UpdateStatus("获取作品列表...");
    auto artworks = GetUserArtworks(uid, types);
    if (artworks.empty()) {
        UpdateStatus("没有找到作品");
        return AppResult::NO_ARTWORKS;
    }
    
    LOG_INFO("找到 {} 个作品", artworks.size());
    
    // 过滤作品
    if (!download_all && !tags.empty()) {
        UpdateStatus("过滤作品...");
        artworks = FilterArtworks(artworks, tags, tag_logic);
        LOG_INFO("过滤后剩余 {} 个作品", artworks.size());
    }
    
    if (artworks.empty()) {
        UpdateStatus("没有符合条件的作品");
        return AppResult::NO_ARTWORKS;
    }
    
    // 生成下载任务
    UpdateStatus("生成下载任务...");
    auto tasks = GenerateDownloadTasks(artworks, *user_info);
    LOG_INFO("生成了 {} 个下载任务", tasks.size());
    
    // 执行下载
    UpdateStatus("开始下载...");
    auto start_time = std::chrono::system_clock::now();
    
    if (!ExecuteDownload(tasks)) {
        return HandleError("下载过程中发生错误", AppResult::DOWNLOAD_ERROR);
    }
    
    // 计算统计信息
    CalculateDownloadStats(tasks, start_time);
    
    // 显示结果
    ShowDownloadResults();
    
    return AppResult::SUCCESS;
}

std::unique_ptr<UserInfo> CoreLogic::GetUserInfo(const std::string& uid) {
    if (!pixiv_api_) {
        LOG_ERROR("PixivAPI未初始化");
        return nullptr;
    }

    return pixiv_api_->GetUserInfo(uid);
}

std::vector<ArtworkMetadata> CoreLogic::GetUserArtworks(const std::string& uid,
                                                        const std::vector<ArtworkType>& types) {
    if (!pixiv_api_) {
        LOG_ERROR("PixivAPI未初始化");
        return {};
    }

    return pixiv_api_->GetUserArtworks(uid, types);
}

std::vector<ArtworkMetadata> CoreLogic::FilterArtworks(const std::vector<ArtworkMetadata>& artworks,
                                                       const std::vector<std::string>& tags,
                                                       TagFilterLogic logic) {
    // 简化实现
    return artworks;
}

std::vector<DownloadTask> CoreLogic::GenerateDownloadTasks(const std::vector<ArtworkMetadata>& artworks,
                                                          const UserInfo& user_info) {
    // 简化实现
    return {};
}

bool CoreLogic::ExecuteDownload(const std::vector<DownloadTask>& tasks) {
    // 简化实现
    return true;
}

void CoreLogic::HandleProgressUpdate(const DownloadProgress& progress) {
    if (progress_callback_) {
        progress_callback_(progress);
    }
}

void CoreLogic::UpdateStatus(const std::string& message) {
    LOG_INFO(message);
    if (status_callback_) {
        status_callback_(message);
    }
}

void CoreLogic::CalculateDownloadStats(const std::vector<DownloadTask>& tasks,
                                      const std::chrono::system_clock::time_point& start_time) {
    auto end_time = std::chrono::system_clock::now();
    download_stats_.elapsed_time = std::chrono::duration<double>(end_time - start_time).count();
    download_stats_.total_artworks = tasks.size();
    // 其他统计信息计算...
}

void CoreLogic::ShowDownloadResults() {
    LOG_INFO("下载完成统计:");
    LOG_INFO("总作品数: {}", download_stats_.total_artworks);
    LOG_INFO("成功: {}", download_stats_.successful_artworks);
    LOG_INFO("失败: {}", download_stats_.failed_artworks);
    LOG_INFO("耗时: {:.2f}秒", download_stats_.elapsed_time);
}

AppResult CoreLogic::HandleError(const std::string& error_message, AppResult result) {
    LOG_ERROR(error_message);
    if (status_callback_) {
        status_callback_("错误: " + error_message);
    }
    return result;
}

bool CoreLogic::ValidateConfiguration() {
    return config_manager_->ValidateConfig();
}

bool CoreLogic::ValidateAuthentication() {
    return auth_manager_->ValidateCookie();
}

void CoreLogic::Cleanup() {
    static bool cleanup_called = false;
    if (cleanup_called) {
        return; // 避免重复清理
    }
    cleanup_called = true;

    // 先记录日志，然后清理资源
    try {
        LOG_INFO("清理资源...");
    } catch (...) {
        // 如果日志系统已关闭，忽略异常
    }

    if (download_manager_) {
        download_manager_->StopDownload();
        download_manager_.reset();
    }

    // 清理其他管理器
    if (auth_manager_) {
        auth_manager_.reset();
    }

    if (config_manager_) {
        config_manager_.reset();
    }

    if (cli_manager_) {
        cli_manager_.reset();
    }
}

void CoreLogic::SetupSignalHandlers() {
    std::signal(SIGINT, HandleInterruptSignal);
    std::signal(SIGTERM, HandleInterruptSignal);
}

void CoreLogic::HandleInterruptSignal(int signal) {
    if (instance_) {
        instance_->Cancel();
        // 执行清理操作
        instance_->Cleanup();
    }
    // 使用正常退出而不是直接exit，避免段错误
    std::_Exit(signal);
}

bool CoreLogic::CheckNetworkConnection() {
    // 简化实现
    return true;
}

bool CoreLogic::PreCheckDownloadConditions(const std::string& uid) {
    // 简化实现
    return true;
}

std::string CoreLogic::AppResultToString(AppResult result) {
    switch (result) {
        case AppResult::SUCCESS: return "成功";
        case AppResult::CONFIG_ERROR: return "配置错误";
        case AppResult::AUTH_ERROR: return "认证错误";
        case AppResult::NETWORK_ERROR: return "网络错误";
        case AppResult::USER_NOT_FOUND: return "用户不存在";
        case AppResult::NO_ARTWORKS: return "没有作品";
        case AppResult::DOWNLOAD_ERROR: return "下载错误";
        case AppResult::STORAGE_ERROR: return "存储错误";
        case AppResult::USER_CANCELLED: return "用户取消";
        default: return "未知错误";
    }
}

int CoreLogic::AppResultToExitCode(AppResult result) {
    return static_cast<int>(result);
}

} // namespace core
} // namespace pixiv
