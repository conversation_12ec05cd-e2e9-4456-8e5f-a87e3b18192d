# This is the CMakeCache file.
# For build in directory: /mnt/wd500g/PixivTagDownloader-CPP/build
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//The path to <PERSON><PERSON><PERSON>'s include directory.
Brotli_INCLUDE_DIR:PATH=/usr/include

//The directory containing a CMake configuration file for CLI11.
CLI11_DIR:PATH=/usr/share/cmake/CLI11

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Debug

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//No help, variable specified on the command line.
CMAKE_POLICY_VERSION_MINIMUM:UNINITIALIZED=3.5

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=高效可靠的Pixiv作品下载器

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=PixivTagDownloader

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.0.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=CMAKE_TAPI-NOTFOUND

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Enable to build Debian packages
CPACK_BINARY_DEB:BOOL=OFF

//Enable to build FreeBSD packages
CPACK_BINARY_FREEBSD:BOOL=OFF

//Enable to build IFW packages
CPACK_BINARY_IFW:BOOL=OFF

//Enable to build NSIS packages
CPACK_BINARY_NSIS:BOOL=OFF

//Enable to build RPM packages
CPACK_BINARY_RPM:BOOL=OFF

//Enable to build STGZ packages
CPACK_BINARY_STGZ:BOOL=ON

//Enable to build TBZ2 packages
CPACK_BINARY_TBZ2:BOOL=OFF

//Enable to build TGZ packages
CPACK_BINARY_TGZ:BOOL=ON

//Enable to build TXZ packages
CPACK_BINARY_TXZ:BOOL=OFF

//Enable to build TZ packages
CPACK_BINARY_TZ:BOOL=ON

//Enable to build RPM source packages
CPACK_SOURCE_RPM:BOOL=OFF

//Enable to build TBZ2 source packages
CPACK_SOURCE_TBZ2:BOOL=ON

//Enable to build TGZ source packages
CPACK_SOURCE_TGZ:BOOL=ON

//Enable to build TXZ source packages
CPACK_SOURCE_TXZ:BOOL=ON

//Enable to build TZ source packages
CPACK_SOURCE_TZ:BOOL=ON

//Enable to build ZIP source packages
CPACK_SOURCE_ZIP:BOOL=OFF

//Path to a library.
OPENSSL_CRYPTO_LIBRARY:FILEPATH=/usr/lib/libcrypto.so

//Path to a file.
OPENSSL_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
OPENSSL_SSL_LIBRARY:FILEPATH=/usr/lib/libssl.so

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Value Computed by CMake
PixivTagDownloader_BINARY_DIR:STATIC=/mnt/wd500g/PixivTagDownloader-CPP/build

//Value Computed by CMake
PixivTagDownloader_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
PixivTagDownloader_SOURCE_DIR:STATIC=/mnt/wd500g/PixivTagDownloader-CPP

//Path to a file.
ZLIB_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
ZLIB_LIBRARY_DEBUG:FILEPATH=ZLIB_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
ZLIB_LIBRARY_RELEASE:FILEPATH=/usr/lib/libz.so

//The directory containing a CMake configuration file for fmt.
fmt_DIR:PATH=/usr/lib/cmake/fmt

//The directory containing a CMake configuration file for httplib.
httplib_DIR:PATH=/usr/lib/cmake/httplib

//The directory containing a CMake configuration file for nlohmann_json.
nlohmann_json_DIR:PATH=/usr/share/cmake/nlohmann_json

//Path to a library.
pkgcfg_lib_Brotli_common_brotlicommon:FILEPATH=/usr/lib/libbrotlicommon.so

//Path to a library.
pkgcfg_lib_Brotli_decoder_brotlidec:FILEPATH=/usr/lib/libbrotlidec.so

//Path to a library.
pkgcfg_lib_Brotli_encoder_brotlienc:FILEPATH=/usr/lib/libbrotlienc.so

//Path to a library.
pkgcfg_lib__OPENSSL_crypto:FILEPATH=/usr/lib/libcrypto.so

//Path to a library.
pkgcfg_lib__OPENSSL_ssl:FILEPATH=/usr/lib/libssl.so

//The directory containing a CMake configuration file for spdlog.
spdlog_DIR:PATH=/usr/lib/cmake/spdlog

//The directory containing a CMake configuration file for yaml-cpp.
yaml-cpp_DIR:PATH=/usr/lib/cmake/yaml-cpp

//The directory containing a CMake configuration file for zstd.
zstd_DIR:PATH=/usr/lib/cmake/zstd


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: Brotli_INCLUDE_DIR
Brotli_INCLUDE_DIR-ADVANCED:INTERNAL=1
Brotli_common_CFLAGS:INTERNAL=-I/usr/include
Brotli_common_CFLAGS_I:INTERNAL=
Brotli_common_CFLAGS_OTHER:INTERNAL=
Brotli_common_FOUND:INTERNAL=1
Brotli_common_INCLUDEDIR:INTERNAL=/usr/include
Brotli_common_INCLUDE_DIRS:INTERNAL=/usr/include
Brotli_common_LDFLAGS:INTERNAL=-L/usr/lib;-lbrotlicommon
Brotli_common_LDFLAGS_OTHER:INTERNAL=
Brotli_common_LIBDIR:INTERNAL=/usr/lib
Brotli_common_LIBRARIES:INTERNAL=brotlicommon
Brotli_common_LIBRARY_DIRS:INTERNAL=/usr/lib
Brotli_common_LIBS:INTERNAL=
Brotli_common_LIBS_L:INTERNAL=
Brotli_common_LIBS_OTHER:INTERNAL=
Brotli_common_LIBS_PATHS:INTERNAL=
Brotli_common_MODULE_NAME:INTERNAL=libbrotlicommon
Brotli_common_PREFIX:INTERNAL=/usr
Brotli_common_STATIC_CFLAGS:INTERNAL=-I/usr/include
Brotli_common_STATIC_CFLAGS_I:INTERNAL=
Brotli_common_STATIC_CFLAGS_OTHER:INTERNAL=
Brotli_common_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include
Brotli_common_STATIC_LDFLAGS:INTERNAL=-L/usr/lib;-lbrotlicommon
Brotli_common_STATIC_LDFLAGS_OTHER:INTERNAL=
Brotli_common_STATIC_LIBDIR:INTERNAL=
Brotli_common_STATIC_LIBRARIES:INTERNAL=brotlicommon
Brotli_common_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib
Brotli_common_STATIC_LIBS:INTERNAL=
Brotli_common_STATIC_LIBS_L:INTERNAL=
Brotli_common_STATIC_LIBS_OTHER:INTERNAL=
Brotli_common_STATIC_LIBS_PATHS:INTERNAL=
Brotli_common_VERSION:INTERNAL=1.1.0
Brotli_common_libbrotlicommon_INCLUDEDIR:INTERNAL=
Brotli_common_libbrotlicommon_LIBDIR:INTERNAL=
Brotli_common_libbrotlicommon_PREFIX:INTERNAL=
Brotli_common_libbrotlicommon_VERSION:INTERNAL=
Brotli_decoder_CFLAGS:INTERNAL=-I/usr/include
Brotli_decoder_CFLAGS_I:INTERNAL=
Brotli_decoder_CFLAGS_OTHER:INTERNAL=
Brotli_decoder_FOUND:INTERNAL=1
Brotli_decoder_INCLUDEDIR:INTERNAL=/usr/include
Brotli_decoder_INCLUDE_DIRS:INTERNAL=/usr/include
Brotli_decoder_LDFLAGS:INTERNAL=-L/usr/lib;-lbrotlidec
Brotli_decoder_LDFLAGS_OTHER:INTERNAL=
Brotli_decoder_LIBDIR:INTERNAL=/usr/lib
Brotli_decoder_LIBRARIES:INTERNAL=brotlidec
Brotli_decoder_LIBRARY_DIRS:INTERNAL=/usr/lib
Brotli_decoder_LIBS:INTERNAL=
Brotli_decoder_LIBS_L:INTERNAL=
Brotli_decoder_LIBS_OTHER:INTERNAL=
Brotli_decoder_LIBS_PATHS:INTERNAL=
Brotli_decoder_MODULE_NAME:INTERNAL=libbrotlidec
Brotli_decoder_PREFIX:INTERNAL=/usr
Brotli_decoder_STATIC_CFLAGS:INTERNAL=-I/usr/include
Brotli_decoder_STATIC_CFLAGS_I:INTERNAL=
Brotli_decoder_STATIC_CFLAGS_OTHER:INTERNAL=
Brotli_decoder_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include
Brotli_decoder_STATIC_LDFLAGS:INTERNAL=-L/usr/lib;-lbrotlidec;-lbrotlicommon
Brotli_decoder_STATIC_LDFLAGS_OTHER:INTERNAL=
Brotli_decoder_STATIC_LIBDIR:INTERNAL=
Brotli_decoder_STATIC_LIBRARIES:INTERNAL=brotlidec;brotlicommon
Brotli_decoder_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib
Brotli_decoder_STATIC_LIBS:INTERNAL=
Brotli_decoder_STATIC_LIBS_L:INTERNAL=
Brotli_decoder_STATIC_LIBS_OTHER:INTERNAL=
Brotli_decoder_STATIC_LIBS_PATHS:INTERNAL=
Brotli_decoder_VERSION:INTERNAL=1.1.0
Brotli_decoder_libbrotlidec_INCLUDEDIR:INTERNAL=
Brotli_decoder_libbrotlidec_LIBDIR:INTERNAL=
Brotli_decoder_libbrotlidec_PREFIX:INTERNAL=
Brotli_decoder_libbrotlidec_VERSION:INTERNAL=
Brotli_encoder_CFLAGS:INTERNAL=-I/usr/include
Brotli_encoder_CFLAGS_I:INTERNAL=
Brotli_encoder_CFLAGS_OTHER:INTERNAL=
Brotli_encoder_FOUND:INTERNAL=1
Brotli_encoder_INCLUDEDIR:INTERNAL=/usr/include
Brotli_encoder_INCLUDE_DIRS:INTERNAL=/usr/include
Brotli_encoder_LDFLAGS:INTERNAL=-L/usr/lib;-lbrotlienc
Brotli_encoder_LDFLAGS_OTHER:INTERNAL=
Brotli_encoder_LIBDIR:INTERNAL=/usr/lib
Brotli_encoder_LIBRARIES:INTERNAL=brotlienc
Brotli_encoder_LIBRARY_DIRS:INTERNAL=/usr/lib
Brotli_encoder_LIBS:INTERNAL=
Brotli_encoder_LIBS_L:INTERNAL=
Brotli_encoder_LIBS_OTHER:INTERNAL=
Brotli_encoder_LIBS_PATHS:INTERNAL=
Brotli_encoder_MODULE_NAME:INTERNAL=libbrotlienc
Brotli_encoder_PREFIX:INTERNAL=/usr
Brotli_encoder_STATIC_CFLAGS:INTERNAL=-I/usr/include
Brotli_encoder_STATIC_CFLAGS_I:INTERNAL=
Brotli_encoder_STATIC_CFLAGS_OTHER:INTERNAL=
Brotli_encoder_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include
Brotli_encoder_STATIC_LDFLAGS:INTERNAL=-L/usr/lib;-lbrotlienc;-lbrotlicommon
Brotli_encoder_STATIC_LDFLAGS_OTHER:INTERNAL=
Brotli_encoder_STATIC_LIBDIR:INTERNAL=
Brotli_encoder_STATIC_LIBRARIES:INTERNAL=brotlienc;brotlicommon
Brotli_encoder_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib
Brotli_encoder_STATIC_LIBS:INTERNAL=
Brotli_encoder_STATIC_LIBS_L:INTERNAL=
Brotli_encoder_STATIC_LIBS_OTHER:INTERNAL=
Brotli_encoder_STATIC_LIBS_PATHS:INTERNAL=
Brotli_encoder_VERSION:INTERNAL=1.1.0
Brotli_encoder_libbrotlienc_INCLUDEDIR:INTERNAL=
Brotli_encoder_libbrotlienc_LIBDIR:INTERNAL=
Brotli_encoder_libbrotlienc_PREFIX:INTERNAL=
Brotli_encoder_libbrotlienc_VERSION:INTERNAL=
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/mnt/wd500g/PixivTagDownloader-CPP/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=0
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=2
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/usr/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/mnt/wd500g/PixivTagDownloader-CPP
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=0
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_DEB
CPACK_BINARY_DEB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_FREEBSD
CPACK_BINARY_FREEBSD-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_IFW
CPACK_BINARY_IFW-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_NSIS
CPACK_BINARY_NSIS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_RPM
CPACK_BINARY_RPM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_STGZ
CPACK_BINARY_STGZ-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_TBZ2
CPACK_BINARY_TBZ2-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_TGZ
CPACK_BINARY_TGZ-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_TXZ
CPACK_BINARY_TXZ-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_TZ
CPACK_BINARY_TZ-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_RPM
CPACK_SOURCE_RPM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_TBZ2
CPACK_SOURCE_TBZ2-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_TGZ
CPACK_SOURCE_TGZ-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_TXZ
CPACK_SOURCE_TXZ-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_TZ
CPACK_SOURCE_TZ-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_ZIP
CPACK_SOURCE_ZIP-ADVANCED:INTERNAL=1
//Details about finding Brotli
FIND_PACKAGE_MESSAGE_DETAILS_Brotli:INTERNAL=[/usr/include][/usr/lib/libbrotlicommon.so][/usr/lib/libbrotlidec.so][/usr/lib/libbrotlienc.so][found components: common encoder decoder ][v()]
//Details about finding OpenSSL
FIND_PACKAGE_MESSAGE_DETAILS_OpenSSL:INTERNAL=[/usr/lib/libcrypto.so][/usr/include][ ][v3.5.0()]
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/usr/bin/pkg-config][v2.4.3()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding ZLIB
FIND_PACKAGE_MESSAGE_DETAILS_ZLIB:INTERNAL=[/usr/lib/libz.so][/usr/include][ ][v1.3.1()]
//Details about finding httplib
FIND_PACKAGE_MESSAGE_DETAILS_httplib:INTERNAL=[/usr/lib/libcpp-httplib.so.0.20.1][/usr/include/httplib.h]
//Details about finding nlohmann_json
FIND_PACKAGE_MESSAGE_DETAILS_nlohmann_json:INTERNAL=[/usr/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake][v3.12.0()]
//ADVANCED property for variable: OPENSSL_CRYPTO_LIBRARY
OPENSSL_CRYPTO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_INCLUDE_DIR
OPENSSL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_SSL_LIBRARY
OPENSSL_SSL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_INCLUDE_DIR
ZLIB_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_DEBUG
ZLIB_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_RELEASE
ZLIB_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
_OPENSSL_CFLAGS:INTERNAL=-I/usr/include
_OPENSSL_CFLAGS_I:INTERNAL=
_OPENSSL_CFLAGS_OTHER:INTERNAL=
_OPENSSL_FOUND:INTERNAL=1
_OPENSSL_INCLUDEDIR:INTERNAL=/usr/include
_OPENSSL_INCLUDE_DIRS:INTERNAL=/usr/include
_OPENSSL_LDFLAGS:INTERNAL=-L/usr/lib;-lssl;-lcrypto
_OPENSSL_LDFLAGS_OTHER:INTERNAL=
_OPENSSL_LIBDIR:INTERNAL=/usr/lib
_OPENSSL_LIBRARIES:INTERNAL=ssl;crypto
_OPENSSL_LIBRARY_DIRS:INTERNAL=/usr/lib
_OPENSSL_LIBS:INTERNAL=
_OPENSSL_LIBS_L:INTERNAL=
_OPENSSL_LIBS_OTHER:INTERNAL=
_OPENSSL_LIBS_PATHS:INTERNAL=
_OPENSSL_MODULE_NAME:INTERNAL=openssl
_OPENSSL_PREFIX:INTERNAL=/usr
_OPENSSL_STATIC_CFLAGS:INTERNAL=-I/usr/include
_OPENSSL_STATIC_CFLAGS_I:INTERNAL=
_OPENSSL_STATIC_CFLAGS_OTHER:INTERNAL=
_OPENSSL_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include
_OPENSSL_STATIC_LDFLAGS:INTERNAL=-L/usr/lib;-lssl;-lcrypto;-ldl;-pthread
_OPENSSL_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
_OPENSSL_STATIC_LIBDIR:INTERNAL=
_OPENSSL_STATIC_LIBRARIES:INTERNAL=ssl;crypto;dl
_OPENSSL_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib
_OPENSSL_STATIC_LIBS:INTERNAL=
_OPENSSL_STATIC_LIBS_L:INTERNAL=
_OPENSSL_STATIC_LIBS_OTHER:INTERNAL=
_OPENSSL_STATIC_LIBS_PATHS:INTERNAL=
_OPENSSL_VERSION:INTERNAL=3.5.0
_OPENSSL_openssl_INCLUDEDIR:INTERNAL=
_OPENSSL_openssl_LIBDIR:INTERNAL=
_OPENSSL_openssl_PREFIX:INTERNAL=
_OPENSSL_openssl_VERSION:INTERNAL=
__pkg_config_arguments_Brotli_common:INTERNAL=QUIET;GLOBAL;IMPORTED_TARGET;libbrotlicommon
__pkg_config_arguments_Brotli_decoder:INTERNAL=QUIET;GLOBAL;IMPORTED_TARGET;libbrotlidec
__pkg_config_arguments_Brotli_encoder:INTERNAL=QUIET;GLOBAL;IMPORTED_TARGET;libbrotlienc
__pkg_config_arguments__OPENSSL:INTERNAL=QUIET;openssl
__pkg_config_checked_Brotli_common:INTERNAL=1
__pkg_config_checked_Brotli_decoder:INTERNAL=1
__pkg_config_checked_Brotli_encoder:INTERNAL=1
__pkg_config_checked__OPENSSL:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_Brotli_common_brotlicommon
pkgcfg_lib_Brotli_common_brotlicommon-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_Brotli_decoder_brotlidec
pkgcfg_lib_Brotli_decoder_brotlidec-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_Brotli_encoder_brotlienc
pkgcfg_lib_Brotli_encoder_brotlienc-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_crypto
pkgcfg_lib__OPENSSL_crypto-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_ssl
pkgcfg_lib__OPENSSL_ssl-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib

