# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/wd500g/PixivTagDownloader-CPP

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/wd500g/PixivTagDownloader-CPP/build

# Include any dependencies generated for this target.
include CMakeFiles/PixivTagDownloader.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/PixivTagDownloader.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/PixivTagDownloader.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/PixivTagDownloader.dir/flags.make

CMakeFiles/PixivTagDownloader.dir/codegen:
.PHONY : CMakeFiles/PixivTagDownloader.dir/codegen

CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o: CMakeFiles/PixivTagDownloader.dir/flags.make
CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o: /mnt/wd500g/PixivTagDownloader-CPP/src/main.cpp
CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o -MF CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o.d -o CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o -c /mnt/wd500g/PixivTagDownloader-CPP/src/main.cpp

CMakeFiles/PixivTagDownloader.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PixivTagDownloader.dir/src/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/wd500g/PixivTagDownloader-CPP/src/main.cpp > CMakeFiles/PixivTagDownloader.dir/src/main.cpp.i

CMakeFiles/PixivTagDownloader.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PixivTagDownloader.dir/src/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/wd500g/PixivTagDownloader-CPP/src/main.cpp -o CMakeFiles/PixivTagDownloader.dir/src/main.cpp.s

CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.o: CMakeFiles/PixivTagDownloader.dir/flags.make
CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.o: /mnt/wd500g/PixivTagDownloader-CPP/src/common/types.cpp
CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.o: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.o -MF CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.o.d -o CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.o -c /mnt/wd500g/PixivTagDownloader-CPP/src/common/types.cpp

CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/wd500g/PixivTagDownloader-CPP/src/common/types.cpp > CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.i

CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/wd500g/PixivTagDownloader-CPP/src/common/types.cpp -o CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.s

CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o: CMakeFiles/PixivTagDownloader.dir/flags.make
CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o: /mnt/wd500g/PixivTagDownloader-CPP/src/utils/string_utils.cpp
CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o -MF CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o.d -o CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o -c /mnt/wd500g/PixivTagDownloader-CPP/src/utils/string_utils.cpp

CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/wd500g/PixivTagDownloader-CPP/src/utils/string_utils.cpp > CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.i

CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/wd500g/PixivTagDownloader-CPP/src/utils/string_utils.cpp -o CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.s

CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.o: CMakeFiles/PixivTagDownloader.dir/flags.make
CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.o: /mnt/wd500g/PixivTagDownloader-CPP/src/utils/file_utils.cpp
CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.o: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.o -MF CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.o.d -o CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.o -c /mnt/wd500g/PixivTagDownloader-CPP/src/utils/file_utils.cpp

CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/wd500g/PixivTagDownloader-CPP/src/utils/file_utils.cpp > CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.i

CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/wd500g/PixivTagDownloader-CPP/src/utils/file_utils.cpp -o CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.s

CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o: CMakeFiles/PixivTagDownloader.dir/flags.make
CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o: /mnt/wd500g/PixivTagDownloader-CPP/src/utils/time_utils.cpp
CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o -MF CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o.d -o CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o -c /mnt/wd500g/PixivTagDownloader-CPP/src/utils/time_utils.cpp

CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/wd500g/PixivTagDownloader-CPP/src/utils/time_utils.cpp > CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.i

CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/wd500g/PixivTagDownloader-CPP/src/utils/time_utils.cpp -o CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.s

CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.o: CMakeFiles/PixivTagDownloader.dir/flags.make
CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.o: /mnt/wd500g/PixivTagDownloader-CPP/src/utils/logger.cpp
CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.o: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.o -MF CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.o.d -o CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.o -c /mnt/wd500g/PixivTagDownloader-CPP/src/utils/logger.cpp

CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/wd500g/PixivTagDownloader-CPP/src/utils/logger.cpp > CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.i

CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/wd500g/PixivTagDownloader-CPP/src/utils/logger.cpp -o CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.s

CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o: CMakeFiles/PixivTagDownloader.dir/flags.make
CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o: /mnt/wd500g/PixivTagDownloader-CPP/src/config/config_manager.cpp
CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o -MF CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o.d -o CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o -c /mnt/wd500g/PixivTagDownloader-CPP/src/config/config_manager.cpp

CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/wd500g/PixivTagDownloader-CPP/src/config/config_manager.cpp > CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.i

CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/wd500g/PixivTagDownloader-CPP/src/config/config_manager.cpp -o CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.s

CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.o: CMakeFiles/PixivTagDownloader.dir/flags.make
CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.o: /mnt/wd500g/PixivTagDownloader-CPP/src/auth/auth_manager.cpp
CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.o: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.o -MF CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.o.d -o CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.o -c /mnt/wd500g/PixivTagDownloader-CPP/src/auth/auth_manager.cpp

CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/wd500g/PixivTagDownloader-CPP/src/auth/auth_manager.cpp > CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.i

CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/wd500g/PixivTagDownloader-CPP/src/auth/auth_manager.cpp -o CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.s

CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o: CMakeFiles/PixivTagDownloader.dir/flags.make
CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o: /mnt/wd500g/PixivTagDownloader-CPP/src/api/pixiv_api.cpp
CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o -MF CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o.d -o CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o -c /mnt/wd500g/PixivTagDownloader-CPP/src/api/pixiv_api.cpp

CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/wd500g/PixivTagDownloader-CPP/src/api/pixiv_api.cpp > CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.i

CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/wd500g/PixivTagDownloader-CPP/src/api/pixiv_api.cpp -o CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.s

CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o: CMakeFiles/PixivTagDownloader.dir/flags.make
CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o: /mnt/wd500g/PixivTagDownloader-CPP/src/storage/storage_manager.cpp
CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o -MF CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o.d -o CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o -c /mnt/wd500g/PixivTagDownloader-CPP/src/storage/storage_manager.cpp

CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/wd500g/PixivTagDownloader-CPP/src/storage/storage_manager.cpp > CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.i

CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/wd500g/PixivTagDownloader-CPP/src/storage/storage_manager.cpp -o CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.s

CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o: CMakeFiles/PixivTagDownloader.dir/flags.make
CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o: /mnt/wd500g/PixivTagDownloader-CPP/src/downloader/download_manager.cpp
CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o -MF CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o.d -o CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o -c /mnt/wd500g/PixivTagDownloader-CPP/src/downloader/download_manager.cpp

CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/wd500g/PixivTagDownloader-CPP/src/downloader/download_manager.cpp > CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.i

CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/wd500g/PixivTagDownloader-CPP/src/downloader/download_manager.cpp -o CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.s

CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.o: CMakeFiles/PixivTagDownloader.dir/flags.make
CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.o: /mnt/wd500g/PixivTagDownloader-CPP/src/downloader/direct_downloader.cpp
CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.o: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.o -MF CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.o.d -o CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.o -c /mnt/wd500g/PixivTagDownloader-CPP/src/downloader/direct_downloader.cpp

CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/wd500g/PixivTagDownloader-CPP/src/downloader/direct_downloader.cpp > CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.i

CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/wd500g/PixivTagDownloader-CPP/src/downloader/direct_downloader.cpp -o CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.s

CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.o: CMakeFiles/PixivTagDownloader.dir/flags.make
CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.o: /mnt/wd500g/PixivTagDownloader-CPP/src/downloader/aria2_downloader.cpp
CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.o: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.o -MF CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.o.d -o CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.o -c /mnt/wd500g/PixivTagDownloader-CPP/src/downloader/aria2_downloader.cpp

CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/wd500g/PixivTagDownloader-CPP/src/downloader/aria2_downloader.cpp > CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.i

CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/wd500g/PixivTagDownloader-CPP/src/downloader/aria2_downloader.cpp -o CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.s

CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.o: CMakeFiles/PixivTagDownloader.dir/flags.make
CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.o: /mnt/wd500g/PixivTagDownloader-CPP/src/cli/cli_manager.cpp
CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.o: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.o -MF CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.o.d -o CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.o -c /mnt/wd500g/PixivTagDownloader-CPP/src/cli/cli_manager.cpp

CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/wd500g/PixivTagDownloader-CPP/src/cli/cli_manager.cpp > CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.i

CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/wd500g/PixivTagDownloader-CPP/src/cli/cli_manager.cpp -o CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.s

CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.o: CMakeFiles/PixivTagDownloader.dir/flags.make
CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.o: /mnt/wd500g/PixivTagDownloader-CPP/src/core/core_logic.cpp
CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.o: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.o -MF CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.o.d -o CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.o -c /mnt/wd500g/PixivTagDownloader-CPP/src/core/core_logic.cpp

CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/wd500g/PixivTagDownloader-CPP/src/core/core_logic.cpp > CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.i

CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/wd500g/PixivTagDownloader-CPP/src/core/core_logic.cpp -o CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.s

# Object files for target PixivTagDownloader
PixivTagDownloader_OBJECTS = \
"CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o" \
"CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.o" \
"CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o" \
"CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.o" \
"CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o" \
"CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.o" \
"CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o" \
"CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.o" \
"CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o" \
"CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o" \
"CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o" \
"CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.o" \
"CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.o" \
"CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.o" \
"CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.o"

# External object files for target PixivTagDownloader
PixivTagDownloader_EXTERNAL_OBJECTS =

bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.o
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.o
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.o
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.o
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.o
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.o
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.o
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.o
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/build.make
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/compiler_depend.ts
bin/pixiv-downloader: /usr/lib/libspdlog.so.1.15.3
bin/pixiv-downloader: /usr/lib/libcpp-httplib.so.0.20.1
bin/pixiv-downloader: /usr/lib/libssl.so
bin/pixiv-downloader: /usr/lib/libcrypto.so
bin/pixiv-downloader: /usr/lib/libz.so
bin/pixiv-downloader: /usr/lib/libfmt.so.11.2.0
bin/pixiv-downloader: /usr/lib/libbrotlicommon.so
bin/pixiv-downloader: /usr/lib/libbrotlienc.so
bin/pixiv-downloader: /usr/lib/libbrotlidec.so
bin/pixiv-downloader: /usr/lib/libzstd.so.1.5.7
bin/pixiv-downloader: CMakeFiles/PixivTagDownloader.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Linking CXX executable bin/pixiv-downloader"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/PixivTagDownloader.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/PixivTagDownloader.dir/build: bin/pixiv-downloader
.PHONY : CMakeFiles/PixivTagDownloader.dir/build

CMakeFiles/PixivTagDownloader.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/PixivTagDownloader.dir/cmake_clean.cmake
.PHONY : CMakeFiles/PixivTagDownloader.dir/clean

CMakeFiles/PixivTagDownloader.dir/depend:
	cd /mnt/wd500g/PixivTagDownloader-CPP/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/wd500g/PixivTagDownloader-CPP /mnt/wd500g/PixivTagDownloader-CPP /mnt/wd500g/PixivTagDownloader-CPP/build /mnt/wd500g/PixivTagDownloader-CPP/build /mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles/PixivTagDownloader.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/PixivTagDownloader.dir/depend

