# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DCPPHTTPLIB_BROTLI_SUPPORT -DCPPHTTPLIB_OPENSSL_SUPPORT -DCPPHTTPLIB_ZLIB_SUPPORT -DCPPHTTPLIB_ZSTD_SUPPORT -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB

CXX_INCLUDES = -I/mnt/wd500g/PixivTagDownloader-CPP/include

CXX_FLAGS = -g -std=c++17 -Wall -Wextra -Wpedantic

