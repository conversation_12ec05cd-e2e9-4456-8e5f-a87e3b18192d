
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/mnt/wd500g/PixivTagDownloader-CPP/src/api/pixiv_api.cpp" "CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o" "gcc" "CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o.d"
  "/mnt/wd500g/PixivTagDownloader-CPP/src/auth/auth_manager.cpp" "CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.o" "gcc" "CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.o.d"
  "/mnt/wd500g/PixivTagDownloader-CPP/src/cli/cli_manager.cpp" "CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.o" "gcc" "CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.o.d"
  "/mnt/wd500g/PixivTagDownloader-CPP/src/common/types.cpp" "CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.o" "gcc" "CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.o.d"
  "/mnt/wd500g/PixivTagDownloader-CPP/src/config/config_manager.cpp" "CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o" "gcc" "CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o.d"
  "/mnt/wd500g/PixivTagDownloader-CPP/src/core/core_logic.cpp" "CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.o" "gcc" "CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.o.d"
  "/mnt/wd500g/PixivTagDownloader-CPP/src/downloader/aria2_downloader.cpp" "CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.o" "gcc" "CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.o.d"
  "/mnt/wd500g/PixivTagDownloader-CPP/src/downloader/direct_downloader.cpp" "CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.o" "gcc" "CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.o.d"
  "/mnt/wd500g/PixivTagDownloader-CPP/src/downloader/download_manager.cpp" "CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o" "gcc" "CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o.d"
  "/mnt/wd500g/PixivTagDownloader-CPP/src/main.cpp" "CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o" "gcc" "CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o.d"
  "/mnt/wd500g/PixivTagDownloader-CPP/src/storage/storage_manager.cpp" "CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o" "gcc" "CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o.d"
  "/mnt/wd500g/PixivTagDownloader-CPP/src/utils/file_utils.cpp" "CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.o" "gcc" "CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.o.d"
  "/mnt/wd500g/PixivTagDownloader-CPP/src/utils/logger.cpp" "CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.o" "gcc" "CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.o.d"
  "/mnt/wd500g/PixivTagDownloader-CPP/src/utils/string_utils.cpp" "CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o" "gcc" "CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o.d"
  "/mnt/wd500g/PixivTagDownloader-CPP/src/utils/time_utils.cpp" "CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o" "gcc" "CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o.d"
  "" "bin/pixiv-downloader" "gcc" "CMakeFiles/PixivTagDownloader.dir/link.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
