file(REMOVE_RECURSE
  "CMakeFiles/PixivTagDownloader.dir/link.d"
  "CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o.d"
  "bin/pixiv-downloader"
  "bin/pixiv-downloader.pdb"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX)
  include(CMakeFiles/PixivTagDownloader.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
