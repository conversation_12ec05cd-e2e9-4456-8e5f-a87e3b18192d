/usr/bin/c++ -g -Wl,--dependency-file=CMakeFiles/PixivTagDownloader.dir/link.d CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.o CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.o CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.o CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.o CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.o CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.o CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.o CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.o -o bin/pixiv-downloader  /usr/lib/libspdlog.so.1.15.3 -lyaml-cpp /usr/lib/libcpp-httplib.so.0.20.1 /usr/lib/libssl.so /usr/lib/libcrypto.so /usr/lib/libz.so /usr/lib/libfmt.so.11.2.0 /usr/lib/libbrotlicommon.so /usr/lib/libbrotlienc.so /usr/lib/libbrotlidec.so /usr/lib/libzstd.so.1.5.7
