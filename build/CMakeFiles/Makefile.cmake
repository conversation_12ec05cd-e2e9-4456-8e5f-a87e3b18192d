# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/mnt/wd500g/PixivTagDownloader-CPP/CMakeLists.txt"
  "CMakeFiles/4.0.2-dirty/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.0.2-dirty/CMakeSystem.cmake"
  "/usr/lib/cmake/fmt/fmt-config-version.cmake"
  "/usr/lib/cmake/fmt/fmt-config.cmake"
  "/usr/lib/cmake/fmt/fmt-targets-none.cmake"
  "/usr/lib/cmake/fmt/fmt-targets.cmake"
  "/usr/lib/cmake/httplib/FindBrotli.cmake"
  "/usr/lib/cmake/httplib/httplibConfig.cmake"
  "/usr/lib/cmake/httplib/httplibConfigVersion.cmake"
  "/usr/lib/cmake/httplib/httplibTargets-release.cmake"
  "/usr/lib/cmake/httplib/httplibTargets.cmake"
  "/usr/lib/cmake/spdlog/spdlogConfig.cmake"
  "/usr/lib/cmake/spdlog/spdlogConfigTargets-none.cmake"
  "/usr/lib/cmake/spdlog/spdlogConfigTargets.cmake"
  "/usr/lib/cmake/spdlog/spdlogConfigVersion.cmake"
  "/usr/lib/cmake/yaml-cpp/yaml-cpp-config-version.cmake"
  "/usr/lib/cmake/yaml-cpp/yaml-cpp-config.cmake"
  "/usr/lib/cmake/yaml-cpp/yaml-cpp-targets-release.cmake"
  "/usr/lib/cmake/yaml-cpp/yaml-cpp-targets.cmake"
  "/usr/lib/cmake/zstd/zstdConfig.cmake"
  "/usr/lib/cmake/zstd/zstdConfigVersion.cmake"
  "/usr/lib/cmake/zstd/zstdTargets-none.cmake"
  "/usr/lib/cmake/zstd/zstdTargets.cmake"
  "/usr/share/cmake/CLI11/CLI11Config.cmake"
  "/usr/share/cmake/CLI11/CLI11ConfigVersion.cmake"
  "/usr/share/cmake/Modules/CMakeCXXCompiler.cmake.in"
  "/usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp"
  "/usr/share/cmake/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake/Modules/CMakeCompilerIdDetection.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompilerSupport.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineSystem.cmake"
  "/usr/share/cmake/Modules/CMakeFindBinUtils.cmake"
  "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake"
  "/usr/share/cmake/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/usr/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/usr/share/cmake/Modules/CMakeParseLibraryArchitecture.cmake"
  "/usr/share/cmake/Modules/CMakeSystem.cmake.in"
  "/usr/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeTestCompilerCommon.cmake"
  "/usr/share/cmake/Modules/CMakeUnixFindMake.cmake"
  "/usr/share/cmake/Modules/CPack.cmake"
  "/usr/share/cmake/Modules/CPackComponent.cmake"
  "/usr/share/cmake/Modules/CheckCXXSourceCompiles.cmake"
  "/usr/share/cmake/Modules/CheckIncludeFileCXX.cmake"
  "/usr/share/cmake/Modules/CheckLibraryExists.cmake"
  "/usr/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/usr/share/cmake/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/CrayClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "/usr/share/cmake/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/OrangeC-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/TIClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/FindOpenSSL.cmake"
  "/usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake/Modules/FindPkgConfig.cmake"
  "/usr/share/cmake/Modules/FindThreads.cmake"
  "/usr/share/cmake/Modules/FindZLIB.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeInspectCXXLinker.cmake"
  "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/share/cmake/Modules/Internal/FeatureTesting.cmake"
  "/usr/share/cmake/Modules/Linker/GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Linker/GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linker/GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-Determine-CXX.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-Initialize.cmake"
  "/usr/share/cmake/Modules/Platform/Linux.cmake"
  "/usr/share/cmake/Modules/Platform/UnixPaths.cmake"
  "/usr/share/cmake/Modules/SelectLibraryConfigurations.cmake"
  "/usr/share/cmake/Templates/CPackConfig.cmake.in"
  "/usr/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake"
  "/usr/share/cmake/nlohmann_json/nlohmann_jsonConfigVersion.cmake"
  "/usr/share/cmake/nlohmann_json/nlohmann_jsonTargets.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/4.0.2-dirty/CMakeSystem.cmake"
  "CMakeFiles/4.0.2-dirty/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.0.2-dirty/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.0.2-dirty/CMakeCXXCompiler.cmake"
  "CPackConfig.cmake"
  "CPackSourceConfig.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/PixivTagDownloader.dir/DependInfo.cmake"
  )
