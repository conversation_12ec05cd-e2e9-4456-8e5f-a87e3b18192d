# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/wd500g/PixivTagDownloader-CPP

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/wd500g/PixivTagDownloader-CPP/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Run CPack packaging tool..."
	/usr/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package
.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Run CPack packaging tool for source..."
	/usr/bin/cpack --config ./CPackSourceConfig.cmake /mnt/wd500g/PixivTagDownloader-CPP/build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source
.PHONY : package_source/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles /mnt/wd500g/PixivTagDownloader-CPP/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named PixivTagDownloader

# Build rule for target.
PixivTagDownloader: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 PixivTagDownloader
.PHONY : PixivTagDownloader

# fast build rule for target.
PixivTagDownloader/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/build
.PHONY : PixivTagDownloader/fast

src/api/pixiv_api.o: src/api/pixiv_api.cpp.o
.PHONY : src/api/pixiv_api.o

# target to build an object file
src/api/pixiv_api.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o
.PHONY : src/api/pixiv_api.cpp.o

src/api/pixiv_api.i: src/api/pixiv_api.cpp.i
.PHONY : src/api/pixiv_api.i

# target to preprocess a source file
src/api/pixiv_api.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.i
.PHONY : src/api/pixiv_api.cpp.i

src/api/pixiv_api.s: src/api/pixiv_api.cpp.s
.PHONY : src/api/pixiv_api.s

# target to generate assembly for a file
src/api/pixiv_api.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.s
.PHONY : src/api/pixiv_api.cpp.s

src/auth/auth_manager.o: src/auth/auth_manager.cpp.o
.PHONY : src/auth/auth_manager.o

# target to build an object file
src/auth/auth_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.o
.PHONY : src/auth/auth_manager.cpp.o

src/auth/auth_manager.i: src/auth/auth_manager.cpp.i
.PHONY : src/auth/auth_manager.i

# target to preprocess a source file
src/auth/auth_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.i
.PHONY : src/auth/auth_manager.cpp.i

src/auth/auth_manager.s: src/auth/auth_manager.cpp.s
.PHONY : src/auth/auth_manager.s

# target to generate assembly for a file
src/auth/auth_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/auth/auth_manager.cpp.s
.PHONY : src/auth/auth_manager.cpp.s

src/cli/cli_manager.o: src/cli/cli_manager.cpp.o
.PHONY : src/cli/cli_manager.o

# target to build an object file
src/cli/cli_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.o
.PHONY : src/cli/cli_manager.cpp.o

src/cli/cli_manager.i: src/cli/cli_manager.cpp.i
.PHONY : src/cli/cli_manager.i

# target to preprocess a source file
src/cli/cli_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.i
.PHONY : src/cli/cli_manager.cpp.i

src/cli/cli_manager.s: src/cli/cli_manager.cpp.s
.PHONY : src/cli/cli_manager.s

# target to generate assembly for a file
src/cli/cli_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/cli/cli_manager.cpp.s
.PHONY : src/cli/cli_manager.cpp.s

src/common/types.o: src/common/types.cpp.o
.PHONY : src/common/types.o

# target to build an object file
src/common/types.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.o
.PHONY : src/common/types.cpp.o

src/common/types.i: src/common/types.cpp.i
.PHONY : src/common/types.i

# target to preprocess a source file
src/common/types.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.i
.PHONY : src/common/types.cpp.i

src/common/types.s: src/common/types.cpp.s
.PHONY : src/common/types.s

# target to generate assembly for a file
src/common/types.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/common/types.cpp.s
.PHONY : src/common/types.cpp.s

src/config/config_manager.o: src/config/config_manager.cpp.o
.PHONY : src/config/config_manager.o

# target to build an object file
src/config/config_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.o
.PHONY : src/config/config_manager.cpp.o

src/config/config_manager.i: src/config/config_manager.cpp.i
.PHONY : src/config/config_manager.i

# target to preprocess a source file
src/config/config_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.i
.PHONY : src/config/config_manager.cpp.i

src/config/config_manager.s: src/config/config_manager.cpp.s
.PHONY : src/config/config_manager.s

# target to generate assembly for a file
src/config/config_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/config/config_manager.cpp.s
.PHONY : src/config/config_manager.cpp.s

src/core/core_logic.o: src/core/core_logic.cpp.o
.PHONY : src/core/core_logic.o

# target to build an object file
src/core/core_logic.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.o
.PHONY : src/core/core_logic.cpp.o

src/core/core_logic.i: src/core/core_logic.cpp.i
.PHONY : src/core/core_logic.i

# target to preprocess a source file
src/core/core_logic.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.i
.PHONY : src/core/core_logic.cpp.i

src/core/core_logic.s: src/core/core_logic.cpp.s
.PHONY : src/core/core_logic.s

# target to generate assembly for a file
src/core/core_logic.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/core/core_logic.cpp.s
.PHONY : src/core/core_logic.cpp.s

src/downloader/aria2_downloader.o: src/downloader/aria2_downloader.cpp.o
.PHONY : src/downloader/aria2_downloader.o

# target to build an object file
src/downloader/aria2_downloader.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.o
.PHONY : src/downloader/aria2_downloader.cpp.o

src/downloader/aria2_downloader.i: src/downloader/aria2_downloader.cpp.i
.PHONY : src/downloader/aria2_downloader.i

# target to preprocess a source file
src/downloader/aria2_downloader.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.i
.PHONY : src/downloader/aria2_downloader.cpp.i

src/downloader/aria2_downloader.s: src/downloader/aria2_downloader.cpp.s
.PHONY : src/downloader/aria2_downloader.s

# target to generate assembly for a file
src/downloader/aria2_downloader.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/downloader/aria2_downloader.cpp.s
.PHONY : src/downloader/aria2_downloader.cpp.s

src/downloader/direct_downloader.o: src/downloader/direct_downloader.cpp.o
.PHONY : src/downloader/direct_downloader.o

# target to build an object file
src/downloader/direct_downloader.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.o
.PHONY : src/downloader/direct_downloader.cpp.o

src/downloader/direct_downloader.i: src/downloader/direct_downloader.cpp.i
.PHONY : src/downloader/direct_downloader.i

# target to preprocess a source file
src/downloader/direct_downloader.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.i
.PHONY : src/downloader/direct_downloader.cpp.i

src/downloader/direct_downloader.s: src/downloader/direct_downloader.cpp.s
.PHONY : src/downloader/direct_downloader.s

# target to generate assembly for a file
src/downloader/direct_downloader.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/downloader/direct_downloader.cpp.s
.PHONY : src/downloader/direct_downloader.cpp.s

src/downloader/download_manager.o: src/downloader/download_manager.cpp.o
.PHONY : src/downloader/download_manager.o

# target to build an object file
src/downloader/download_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o
.PHONY : src/downloader/download_manager.cpp.o

src/downloader/download_manager.i: src/downloader/download_manager.cpp.i
.PHONY : src/downloader/download_manager.i

# target to preprocess a source file
src/downloader/download_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.i
.PHONY : src/downloader/download_manager.cpp.i

src/downloader/download_manager.s: src/downloader/download_manager.cpp.s
.PHONY : src/downloader/download_manager.s

# target to generate assembly for a file
src/downloader/download_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.s
.PHONY : src/downloader/download_manager.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/storage/storage_manager.o: src/storage/storage_manager.cpp.o
.PHONY : src/storage/storage_manager.o

# target to build an object file
src/storage/storage_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o
.PHONY : src/storage/storage_manager.cpp.o

src/storage/storage_manager.i: src/storage/storage_manager.cpp.i
.PHONY : src/storage/storage_manager.i

# target to preprocess a source file
src/storage/storage_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.i
.PHONY : src/storage/storage_manager.cpp.i

src/storage/storage_manager.s: src/storage/storage_manager.cpp.s
.PHONY : src/storage/storage_manager.s

# target to generate assembly for a file
src/storage/storage_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.s
.PHONY : src/storage/storage_manager.cpp.s

src/utils/file_utils.o: src/utils/file_utils.cpp.o
.PHONY : src/utils/file_utils.o

# target to build an object file
src/utils/file_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.o
.PHONY : src/utils/file_utils.cpp.o

src/utils/file_utils.i: src/utils/file_utils.cpp.i
.PHONY : src/utils/file_utils.i

# target to preprocess a source file
src/utils/file_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.i
.PHONY : src/utils/file_utils.cpp.i

src/utils/file_utils.s: src/utils/file_utils.cpp.s
.PHONY : src/utils/file_utils.s

# target to generate assembly for a file
src/utils/file_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/file_utils.cpp.s
.PHONY : src/utils/file_utils.cpp.s

src/utils/logger.o: src/utils/logger.cpp.o
.PHONY : src/utils/logger.o

# target to build an object file
src/utils/logger.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.o
.PHONY : src/utils/logger.cpp.o

src/utils/logger.i: src/utils/logger.cpp.i
.PHONY : src/utils/logger.i

# target to preprocess a source file
src/utils/logger.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.i
.PHONY : src/utils/logger.cpp.i

src/utils/logger.s: src/utils/logger.cpp.s
.PHONY : src/utils/logger.s

# target to generate assembly for a file
src/utils/logger.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/logger.cpp.s
.PHONY : src/utils/logger.cpp.s

src/utils/string_utils.o: src/utils/string_utils.cpp.o
.PHONY : src/utils/string_utils.o

# target to build an object file
src/utils/string_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o
.PHONY : src/utils/string_utils.cpp.o

src/utils/string_utils.i: src/utils/string_utils.cpp.i
.PHONY : src/utils/string_utils.i

# target to preprocess a source file
src/utils/string_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.i
.PHONY : src/utils/string_utils.cpp.i

src/utils/string_utils.s: src/utils/string_utils.cpp.s
.PHONY : src/utils/string_utils.s

# target to generate assembly for a file
src/utils/string_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.s
.PHONY : src/utils/string_utils.cpp.s

src/utils/time_utils.o: src/utils/time_utils.cpp.o
.PHONY : src/utils/time_utils.o

# target to build an object file
src/utils/time_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o
.PHONY : src/utils/time_utils.cpp.o

src/utils/time_utils.i: src/utils/time_utils.cpp.i
.PHONY : src/utils/time_utils.i

# target to preprocess a source file
src/utils/time_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.i
.PHONY : src/utils/time_utils.cpp.i

src/utils/time_utils.s: src/utils/time_utils.cpp.s
.PHONY : src/utils/time_utils.s

# target to generate assembly for a file
src/utils/time_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.s
.PHONY : src/utils/time_utils.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... package"
	@echo "... package_source"
	@echo "... rebuild_cache"
	@echo "... PixivTagDownloader"
	@echo "... src/api/pixiv_api.o"
	@echo "... src/api/pixiv_api.i"
	@echo "... src/api/pixiv_api.s"
	@echo "... src/auth/auth_manager.o"
	@echo "... src/auth/auth_manager.i"
	@echo "... src/auth/auth_manager.s"
	@echo "... src/cli/cli_manager.o"
	@echo "... src/cli/cli_manager.i"
	@echo "... src/cli/cli_manager.s"
	@echo "... src/common/types.o"
	@echo "... src/common/types.i"
	@echo "... src/common/types.s"
	@echo "... src/config/config_manager.o"
	@echo "... src/config/config_manager.i"
	@echo "... src/config/config_manager.s"
	@echo "... src/core/core_logic.o"
	@echo "... src/core/core_logic.i"
	@echo "... src/core/core_logic.s"
	@echo "... src/downloader/aria2_downloader.o"
	@echo "... src/downloader/aria2_downloader.i"
	@echo "... src/downloader/aria2_downloader.s"
	@echo "... src/downloader/direct_downloader.o"
	@echo "... src/downloader/direct_downloader.i"
	@echo "... src/downloader/direct_downloader.s"
	@echo "... src/downloader/download_manager.o"
	@echo "... src/downloader/download_manager.i"
	@echo "... src/downloader/download_manager.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/storage/storage_manager.o"
	@echo "... src/storage/storage_manager.i"
	@echo "... src/storage/storage_manager.s"
	@echo "... src/utils/file_utils.o"
	@echo "... src/utils/file_utils.i"
	@echo "... src/utils/file_utils.s"
	@echo "... src/utils/logger.o"
	@echo "... src/utils/logger.i"
	@echo "... src/utils/logger.s"
	@echo "... src/utils/string_utils.o"
	@echo "... src/utils/string_utils.i"
	@echo "... src/utils/string_utils.s"
	@echo "... src/utils/time_utils.o"
	@echo "... src/utils/time_utils.i"
	@echo "... src/utils/time_utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

